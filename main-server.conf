# HTTP服务器配置 - 直接提供服务（不重定向）
server {
    listen 80;
    server_name www.liuyi.site liuyi.site;

    # 日志配置
    access_log /var/log/nginx/liuyi.site.http.access.log;
    error_log /var/log/nginx/liuyi.site.http.error.log;

    # 代理配置到Docker容器服务
    location / {

        proxy_pass http://**********:3100;
        # proxy_pass http://127.0.0.1:3100;

        # 代理头配置 - 重要：让后端服务知道真实的客户端信息
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header X-Forwarded-Port $server_port;

        # 连接和超时配置 - 增加超时时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;

        # WebSocket支持（如果需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 禁用代理缓存（适用于动态内容）
        proxy_cache_bypass $http_upgrade;
    }

    # 健康检查端点（可选）
    location /nginx-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

# HTTPS服务器配置
server {
    listen 443 ssl;
    server_name www.liuyi.site liuyi.site;
    
    # SSL证书配置
    ssl_certificate /etc/nginx/cert/fullchain.pem;
    ssl_certificate_key /etc/nginx/cert/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA:ECDHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA256:AES256-SHA256:AES128-SHA:AES256-SHA:DES-CBC3-SHA;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头配置（HSTS已移除，因为同时支持HTTP和HTTPS）
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 日志配置
    access_log /var/log/nginx/liuyi.site.access.log;
    error_log /var/log/nginx/liuyi.site.error.log;
    
    # 代理配置到Docker容器服务
    location / {

        proxy_pass http://**********:3100;
        # proxy_pass http://127.0.0.1:3100;

        # 代理头配置 - 重要：让后端服务知道真实的客户端信息
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header X-Forwarded-Port $server_port;

        # 连接和超时配置 - 增加超时时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 禁用代理缓存（适用于动态内容）
        proxy_cache_bypass $http_upgrade;
    }
}
