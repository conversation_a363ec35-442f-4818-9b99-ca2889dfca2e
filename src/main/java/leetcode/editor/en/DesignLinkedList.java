package com.ray.en;

import java.util.*;
import com.ray.common.*;

public class DesignLinkedList {

    //leetcode submit region begin(Prohibit modification and deletion)
    class MyLinkedList {
    
        public MyLinkedList() {
            
        }
        
        public int get(int index) {
            
        }
        
        public void addAtHead(int val) {
            
        }
        
        public void addAtTail(int val) {
            
        }
        
        public void addAtIndex(int index, int val) {
            
        }
        
        public void deleteAtIndex(int index) {
            
        }
    }
    
    /**
     * Your MyLinkedList object will be instantiated and called as such:
     * MyLinkedList obj = new MyLinkedList();
     * int param_1 = obj.get(index);
     * obj.addAtHead(val);
     * obj.addAtTail(val);
     * obj.addAtIndex(index,val);
     * obj.deleteAtIndex(index);
     */
    //leetcode submit region end(Prohibit modification and deletion)

    
    public static void main(String[] args) {
        Solution solution = new DesignLinkedList().new Solution();
        // put your test code here
        
    }
}