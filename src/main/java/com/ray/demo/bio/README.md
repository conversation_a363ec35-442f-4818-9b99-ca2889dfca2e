# BIO (Blocking I/O) 演示代码

本目录包含了Java BIO（阻塞IO）的完整演示代码，展示了客户端和服务端是如何交互的。

## 文件说明

- `BIOServer.java` - BIO服务端实现
- `BIOClient.java` - BIO客户端实现  
- `BIOMultiClientTest.java` - 多客户端测试类
- `README.md` - 说明文档

## BIO特点

### 优点
- **编程简单**: 同步阻塞模型，代码逻辑清晰易懂
- **资源占用少**: 对于少量连接，资源使用效率高
- **调试方便**: 线性执行流程，便于调试和排错

### 缺点
- **并发能力有限**: 每个连接需要一个线程，线程数量受限
- **资源消耗大**: 大量连接时会创建大量线程，消耗内存和CPU
- **阻塞等待**: 线程在IO操作时会阻塞，无法处理其他任务

## 运行步骤

### 1. 启动服务器
```bash
# 编译
javac -d target/classes src/main/java/com/ray/demo/bio/*.java

# 运行服务器
java -cp target/classes com.ray.demo.bio.BIOServer
```

服务器启动后会显示：
```
BIO服务器启动成功，监听端口: 8080
等待客户端连接...
```

### 2. 启动单个客户端
在新的终端窗口中运行：
```bash
java -cp target/classes com.ray.demo.bio.BIOClient
```

客户端连接成功后可以：
- 输入任意消息发送给服务器
- 服务器会回显消息
- 输入 `quit` 退出连接

### 3. 运行多客户端测试
在新的终端窗口中运行：
```bash
java -cp target/classes com.ray.demo.bio.BIOMultiClientTest
```

这会同时启动5个客户端连接服务器，用于测试BIO的并发处理能力。

## 交互流程

### 服务端流程
1. **启动监听**: 在指定端口创建ServerSocket
2. **接受连接**: 调用accept()方法阻塞等待客户端连接
3. **创建处理线程**: 为每个客户端连接创建独立的处理线程
4. **处理请求**: 在线程中循环读取客户端消息并回复
5. **清理资源**: 连接断开时清理相关资源

### 客户端流程
1. **建立连接**: 创建Socket连接到服务器
2. **创建IO流**: 建立输入输出流用于通信
3. **发送消息**: 向服务器发送消息
4. **接收回复**: 阻塞等待并接收服务器回复
5. **断开连接**: 发送quit消息或直接关闭连接

### 关键阻塞点
- **ServerSocket.accept()**: 服务器等待客户端连接
- **BufferedReader.readLine()**: 读取数据时阻塞等待
- **Socket连接建立**: 客户端连接服务器时可能阻塞

## 性能特征

### 适用场景
- 连接数较少（通常<1000）
- 连接时间较长
- 对实时性要求不高
- 简单的请求-响应模式

### 不适用场景
- 高并发场景（>10000连接）
- 短连接频繁建立断开
- 需要高吞吐量的场景
- 对响应时间要求极高的场景

## 观察要点

运行演示代码时，可以观察以下现象：

1. **线程使用**: 每个客户端连接都会在服务器端创建一个处理线程
2. **阻塞行为**: 客户端发送消息后会阻塞等待服务器回复
3. **资源消耗**: 多客户端连接时可以观察到线程数量的增加
4. **并发限制**: 线程池大小限制了同时处理的连接数

## 扩展思考

1. **线程池大小**: 如何确定合适的线程池大小？
2. **连接超时**: 如何处理长时间无响应的连接？
3. **异常处理**: 网络异常时如何优雅地处理？
4. **性能优化**: 在BIO模型下还有哪些优化空间？

## 与其他IO模型对比

- **NIO**: 非阻塞IO，使用选择器管理多个通道
- **AIO**: 异步IO，基于回调机制处理IO完成事件
- **Netty**: 基于NIO的高性能网络框架

BIO是最基础的IO模型，理解BIO有助于更好地理解其他高级IO模型的设计思想。
