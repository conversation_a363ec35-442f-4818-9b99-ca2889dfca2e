package com.ray.demo.bio;

import java.io.*;
import java.net.Socket;
import java.util.Scanner;

/**
 * BIO (Blocking I/O) 客户端演示
 * 
 * 特点：
 * 1. 连接建立后，读写操作都是阻塞的
 * 2. 简单直观，适合简单的客户端-服务端通信
 * 3. 一个线程处理一个连接
 * 
 * <AUTHOR>
 */
public class BIOClient {
    
    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 8080;
    
    private Socket socket;
    private BufferedReader reader;
    private PrintWriter writer;
    private Scanner scanner;
    
    /**
     * 连接到服务器
     */
    public boolean connect() {
        try {
            System.out.println("正在连接到服务器 " + SERVER_HOST + ":" + SERVER_PORT + "...");
            
            // 创建Socket连接，这里会阻塞直到连接建立或超时
            socket = new Socket(SERVER_HOST, SERVER_PORT);
            
            // 创建输入输出流
            reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
            writer = new PrintWriter(socket.getOutputStream(), true);
            scanner = new Scanner(System.in);
            
            System.out.println("成功连接到服务器！");
            return true;
            
        } catch (IOException e) {
            System.err.println("连接服务器失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 启动客户端交互
     */
    public void start() {
        if (!connect()) {
            return;
        }
        
        try {
            // 启动接收服务器消息的线程
            Thread receiveThread = new Thread(this::receiveMessages);
            receiveThread.setDaemon(true);
            receiveThread.start();
            
            // 主线程处理用户输入
            handleUserInput();
            
        } catch (Exception e) {
            System.err.println("客户端运行时发生错误: " + e.getMessage());
        } finally {
            disconnect();
        }
    }
    
    /**
     * 处理用户输入
     */
    private void handleUserInput() {
        System.out.println("\n=== BIO客户端交互界面 ===");
        System.out.println("输入消息发送给服务器，输入'quit'退出");
        System.out.print("> ");
        
        String userInput;
        while (scanner.hasNextLine()) {
            userInput = scanner.nextLine().trim();
            
            if (userInput.isEmpty()) {
                System.out.print("> ");
                continue;
            }
            
            // 发送消息到服务器
            writer.println(userInput);
            
            // 如果用户输入quit，退出循环
            if ("quit".equalsIgnoreCase(userInput)) {
                break;
            }
            
            System.out.print("> ");
        }
    }
    
    /**
     * 接收服务器消息的线程方法
     */
    private void receiveMessages() {
        try {
            String serverMessage;
            // 循环接收服务器消息，readLine()会阻塞直到收到数据
            while ((serverMessage = reader.readLine()) != null) {
                System.out.println("\n[服务器] " + serverMessage);
                System.out.print("> ");
            }
        } catch (IOException e) {
            if (!socket.isClosed()) {
                System.err.println("\n接收服务器消息时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 断开连接
     */
    public void disconnect() {
        try {
            if (scanner != null) {
                scanner.close();
            }
            if (reader != null) {
                reader.close();
            }
            if (writer != null) {
                writer.close();
            }
            if (socket != null && !socket.isClosed()) {
                socket.close();
            }
            System.out.println("已断开与服务器的连接");
        } catch (IOException e) {
            System.err.println("断开连接时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        BIOClient client = new BIOClient();
        
        // 添加关闭钩子，确保客户端优雅关闭
        Runtime.getRuntime().addShutdownHook(new Thread(client::disconnect));
        
        // 启动客户端
        client.start();
    }
}
