package com.ray.demo.bio;

import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * BIO (Blocking I/O) 服务端演示
 * 
 * 特点：
 * 1. 每个客户端连接都需要一个独立的线程处理
 * 2. 线程在读写操作时会阻塞
 * 3. 适合连接数较少的场景
 * 
 * <AUTHOR>
 */
public class BIOServer {
    
    private static final int PORT = 8080;
    private static final int THREAD_POOL_SIZE = 10;
    
    private ServerSocket serverSocket;
    private ExecutorService threadPool;
    private volatile boolean running = false;
    
    public BIOServer() {
        // 创建固定大小的线程池，避免无限制创建线程
        this.threadPool = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
    }
    
    /**
     * 启动服务器
     */
    public void start() {
        try {
            serverSocket = new ServerSocket(PORT);
            running = true;
            System.out.println("BIO服务器启动成功，监听端口: " + PORT);
            System.out.println("等待客户端连接...");
            
            // 主线程循环接受客户端连接
            while (running) {
                try {
                    // accept()方法会阻塞，直到有客户端连接
                    Socket clientSocket = serverSocket.accept();
                    System.out.println("新客户端连接: " + clientSocket.getRemoteSocketAddress());
                    
                    // 将客户端处理任务提交给线程池
                    threadPool.submit(new ClientHandler(clientSocket));
                    
                } catch (IOException e) {
                    if (running) {
                        System.err.println("接受客户端连接时发生错误: " + e.getMessage());
                    }
                }
            }
            
        } catch (IOException e) {
            System.err.println("服务器启动失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止服务器
     */
    public void stop() {
        running = false;
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
            }
            if (threadPool != null && !threadPool.isShutdown()) {
                threadPool.shutdown();
            }
            System.out.println("BIO服务器已停止");
        } catch (IOException e) {
            System.err.println("停止服务器时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 客户端处理器
     * 每个客户端连接都会创建一个ClientHandler实例在独立线程中运行
     */
    private static class ClientHandler implements Runnable {
        
        private final Socket clientSocket;
        private BufferedReader reader;
        private PrintWriter writer;
        
        public ClientHandler(Socket clientSocket) {
            this.clientSocket = clientSocket;
        }
        
        @Override
        public void run() {
            String clientAddress = clientSocket.getRemoteSocketAddress().toString();
            System.out.println("开始处理客户端: " + clientAddress);
            
            try {
                // 创建输入输出流
                reader = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
                writer = new PrintWriter(clientSocket.getOutputStream(), true);
                
                // 向客户端发送欢迎消息
                writer.println("欢迎连接到BIO服务器！");
                writer.println("请输入消息，输入'quit'退出连接");
                
                String inputLine;
                // 循环读取客户端消息，readLine()会阻塞直到收到数据
                while ((inputLine = reader.readLine()) != null) {
                    System.out.println("收到来自 " + clientAddress + " 的消息: " + inputLine);
                    
                    // 如果客户端发送quit，则断开连接
                    if ("quit".equalsIgnoreCase(inputLine.trim())) {
                        writer.println("再见！连接即将关闭。");
                        break;
                    }
                    
                    // 回显消息给客户端
                    String response = "服务器回复: " + inputLine + " (已收到)";
                    writer.println(response);
                    
                    // 模拟处理时间
                    Thread.sleep(100);
                }
                
            } catch (IOException e) {
                System.err.println("处理客户端 " + clientAddress + " 时发生IO错误: " + e.getMessage());
            } catch (InterruptedException e) {
                System.err.println("处理客户端 " + clientAddress + " 时被中断: " + e.getMessage());
                Thread.currentThread().interrupt();
            } finally {
                // 清理资源
                closeResources(clientAddress);
            }
        }
        
        /**
         * 关闭资源
         */
        private void closeResources(String clientAddress) {
            try {
                if (reader != null) {
                    reader.close();
                }
                if (writer != null) {
                    writer.close();
                }
                if (clientSocket != null && !clientSocket.isClosed()) {
                    clientSocket.close();
                }
                System.out.println("客户端 " + clientAddress + " 连接已关闭");
            } catch (IOException e) {
                System.err.println("关闭客户端 " + clientAddress + " 资源时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        BIOServer server = new BIOServer();
        
        // 添加关闭钩子，确保服务器优雅关闭
        Runtime.getRuntime().addShutdownHook(new Thread(server::stop));
        
        // 启动服务器
        server.start();
    }
}
