package com.ray.demo.bio;

import java.io.*;
import java.net.Socket;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 验证accept()方法在多线程场景下的并发连接能力
 * 
 * <AUTHOR>
 */
public class AcceptConcurrencyTest {
    
    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 8080;
    
    /**
     * 并发连接测试客户端
     */
    private static class ConcurrentClient implements Runnable {
        
        private final int clientId;
        private final CountDownLatch startLatch;
        private final CountDownLatch finishLatch;
        
        public ConcurrentClient(int clientId, CountDownLatch startLatch, CountDownLatch finishLatch) {
            this.clientId = clientId;
            this.startLatch = startLatch;
            this.finishLatch = finishLatch;
        }
        
        @Override
        public void run() {
            try {
                // 等待统一开始信号
                startLatch.await();
                
                long connectStartTime = System.currentTimeMillis();
                System.out.println("客户端-" + clientId + " 开始连接... [" + connectStartTime + "]");
                
                // 尝试连接服务器
                Socket socket = new Socket(SERVER_HOST, SERVER_PORT);
                
                long connectEndTime = System.currentTimeMillis();
                System.out.println("客户端-" + clientId + " 连接成功! 耗时: " + 
                                 (connectEndTime - connectStartTime) + "ms [" + connectEndTime + "]");
                
                // 创建IO流
                BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                PrintWriter writer = new PrintWriter(socket.getOutputStream(), true);
                
                // 读取服务器欢迎消息
                String welcome1 = reader.readLine();
                String welcome2 = reader.readLine();
                System.out.println("客户端-" + clientId + " 收到欢迎消息: " + welcome1);
                
                // 发送一条测试消息
                String testMessage = "来自客户端-" + clientId + " 的测试消息";
                writer.println(testMessage);
                System.out.println("客户端-" + clientId + " 发送消息: " + testMessage);
                
                // 接收服务器回复
                String response = reader.readLine();
                System.out.println("客户端-" + clientId + " 收到回复: " + response);
                
                // 保持连接一段时间，模拟长连接
                Thread.sleep(2000);
                
                // 发送退出消息
                writer.println("quit");
                String goodbye = reader.readLine();
                System.out.println("客户端-" + clientId + " 收到再见消息: " + goodbye);
                
                // 关闭连接
                socket.close();
                System.out.println("客户端-" + clientId + " 已断开连接");
                
            } catch (Exception e) {
                System.err.println("客户端-" + clientId + " 发生异常: " + e.getMessage());
            } finally {
                finishLatch.countDown();
            }
        }
    }
    
    /**
     * 测试并发连接
     */
    public static void testConcurrentConnections(int clientCount) {
        System.out.println("=== Accept并发连接测试 ===");
        System.out.println("将同时启动 " + clientCount + " 个客户端连接服务器");
        System.out.println("观察accept()方法是否能够并发处理多个连接请求");
        System.out.println();
        
        // 检查服务器是否运行
        if (!isServerRunning()) {
            System.err.println("错误: 服务器未运行，请先启动BIO服务器");
            return;
        }
        
        ExecutorService executor = Executors.newFixedThreadPool(clientCount);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(clientCount);
        
        long testStartTime = System.currentTimeMillis();
        
        // 创建并启动所有客户端
        for (int i = 1; i <= clientCount; i++) {
            executor.submit(new ConcurrentClient(i, startLatch, finishLatch));
        }
        
        System.out.println("所有客户端已准备就绪，开始并发连接测试...");
        System.out.println("测试开始时间: " + testStartTime);
        System.out.println();
        
        // 统一开始连接
        startLatch.countDown();
        
        try {
            // 等待所有客户端完成
            finishLatch.await();
            
            long testEndTime = System.currentTimeMillis();
            long totalTime = testEndTime - testStartTime;
            
            System.out.println();
            System.out.println("=== 测试结果 ===");
            System.out.println("测试完成时间: " + testEndTime);
            System.out.println("总耗时: " + totalTime + " ms");
            System.out.println("平均连接建立时间: " + (totalTime / clientCount) + " ms");
            System.out.println();
            
            System.out.println("=== 结论 ===");
            System.out.println("✅ 所有 " + clientCount + " 个客户端都成功连接到服务器");
            System.out.println("✅ accept()方法虽然是阻塞的，但能够快速处理多个并发连接请求");
            System.out.println("✅ 每个连接都在独立的线程中处理，实现了真正的并发");
            
            if (totalTime < clientCount * 100) {
                System.out.println("✅ 连接建立速度很快，说明accept()循环效率很高");
            }
            
        } catch (InterruptedException e) {
            System.err.println("等待测试完成时被中断: " + e.getMessage());
            Thread.currentThread().interrupt();
        } finally {
            executor.shutdown();
        }
    }
    
    /**
     * 检查服务器是否运行
     */
    private static boolean isServerRunning() {
        try (Socket testSocket = new Socket(SERVER_HOST, SERVER_PORT)) {
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        int clientCount = args.length > 0 ? Integer.parseInt(args[0]) : 5;
        testConcurrentConnections(clientCount);
    }
}
