package com.ray.demo.bio;

import java.io.*;
import java.net.Socket;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * BIO多客户端测试类
 * 
 * 用于演示BIO服务器处理多个客户端连接的情况
 * 可以观察到BIO的阻塞特性和线程使用情况
 * 
 * <AUTHOR>
 */
public class BIOMultiClientTest {
    
    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 8080;
    private static final int CLIENT_COUNT = 5;
    
    /**
     * 模拟客户端
     */
    private static class TestClient implements Runnable {
        
        private final int clientId;
        private final CountDownLatch latch;
        
        public TestClient(int clientId, CountDownLatch latch) {
            this.clientId = clientId;
            this.latch = latch;
        }
        
        @Override
        public void run() {
            Socket socket = null;
            BufferedReader reader = null;
            PrintWriter writer = null;
            
            try {
                System.out.println("客户端-" + clientId + " 开始连接服务器...");
                
                // 连接服务器
                socket = new Socket(SERVER_HOST, SERVER_PORT);
                reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                writer = new PrintWriter(socket.getOutputStream(), true);
                
                System.out.println("客户端-" + clientId + " 连接成功！");
                
                // 读取服务器的欢迎消息
                String welcomeMsg1 = reader.readLine();
                String welcomeMsg2 = reader.readLine();
                System.out.println("客户端-" + clientId + " 收到: " + welcomeMsg1);
                System.out.println("客户端-" + clientId + " 收到: " + welcomeMsg2);
                
                // 发送多条测试消息
                for (int i = 1; i <= 3; i++) {
                    String message = "客户端-" + clientId + " 的第" + i + "条消息";
                    System.out.println("客户端-" + clientId + " 发送: " + message);
                    writer.println(message);
                    
                    // 读取服务器回复
                    String response = reader.readLine();
                    System.out.println("客户端-" + clientId + " 收到回复: " + response);
                    
                    // 模拟处理间隔
                    Thread.sleep(1000);
                }
                
                // 发送退出消息
                System.out.println("客户端-" + clientId + " 发送退出消息");
                writer.println("quit");
                
                // 读取服务器的再见消息
                String goodbyeMsg = reader.readLine();
                System.out.println("客户端-" + clientId + " 收到: " + goodbyeMsg);
                
            } catch (IOException e) {
                System.err.println("客户端-" + clientId + " IO错误: " + e.getMessage());
            } catch (InterruptedException e) {
                System.err.println("客户端-" + clientId + " 被中断: " + e.getMessage());
                Thread.currentThread().interrupt();
            } finally {
                // 清理资源
                closeResources(socket, reader, writer);
                System.out.println("客户端-" + clientId + " 已断开连接");
                latch.countDown();
            }
        }
        
        private void closeResources(Socket socket, BufferedReader reader, PrintWriter writer) {
            try {
                if (reader != null) reader.close();
                if (writer != null) writer.close();
                if (socket != null && !socket.isClosed()) socket.close();
            } catch (IOException e) {
                System.err.println("客户端-" + clientId + " 关闭资源时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 启动多客户端测试
     */
    public static void startMultiClientTest() {
        System.out.println("=== BIO多客户端测试开始 ===");
        System.out.println("将启动 " + CLIENT_COUNT + " 个客户端同时连接服务器");
        System.out.println("请确保BIO服务器已经启动在端口 " + SERVER_PORT);
        System.out.println();
        
        ExecutorService executor = Executors.newFixedThreadPool(CLIENT_COUNT);
        CountDownLatch latch = new CountDownLatch(CLIENT_COUNT);
        
        long startTime = System.currentTimeMillis();
        
        // 启动多个客户端
        for (int i = 1; i <= CLIENT_COUNT; i++) {
            executor.submit(new TestClient(i, latch));
            
            // 稍微错开启动时间，避免同时连接
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        try {
            // 等待所有客户端完成
            latch.await();
            long endTime = System.currentTimeMillis();
            
            System.out.println();
            System.out.println("=== BIO多客户端测试完成 ===");
            System.out.println("总耗时: " + (endTime - startTime) + " 毫秒");
            System.out.println("所有 " + CLIENT_COUNT + " 个客户端都已完成测试");
            
        } catch (InterruptedException e) {
            System.err.println("等待客户端完成时被中断: " + e.getMessage());
            Thread.currentThread().interrupt();
        } finally {
            executor.shutdown();
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        // 检查服务器是否可连接
        if (!isServerRunning()) {
            System.err.println("错误: 无法连接到服务器 " + SERVER_HOST + ":" + SERVER_PORT);
            System.err.println("请先启动BIO服务器 (运行 BIOServer.main())");
            return;
        }
        
        startMultiClientTest();
    }
    
    /**
     * 检查服务器是否运行
     */
    private static boolean isServerRunning() {
        try (Socket testSocket = new Socket(SERVER_HOST, SERVER_PORT)) {
            return true;
        } catch (IOException e) {
            return false;
        }
    }
}
