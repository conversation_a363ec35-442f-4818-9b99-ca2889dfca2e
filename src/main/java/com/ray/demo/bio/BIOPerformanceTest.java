package com.ray.demo.bio;

import java.io.*;
import java.net.Socket;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * BIO性能测试类
 * 
 * 用于测试BIO服务器在不同负载下的性能表现
 * 包括连接建立时间、消息处理时间、并发能力等指标
 * 
 * <AUTHOR>
 */
public class BIOPerformanceTest {
    
    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 8080;
    
    // 测试统计数据
    private static final AtomicInteger successfulConnections = new AtomicInteger(0);
    private static final AtomicInteger failedConnections = new AtomicInteger(0);
    private static final AtomicLong totalResponseTime = new AtomicLong(0);
    private static final AtomicInteger totalMessages = new AtomicInteger(0);
    
    /**
     * 性能测试客户端
     */
    private static class PerformanceTestClient implements Callable<TestResult> {
        
        private final int clientId;
        private final int messageCount;
        private final CountDownLatch startLatch;
        
        public PerformanceTestClient(int clientId, int messageCount, CountDownLatch startLatch) {
            this.clientId = clientId;
            this.messageCount = messageCount;
            this.startLatch = startLatch;
        }
        
        @Override
        public TestResult call() {
            TestResult result = new TestResult(clientId);
            Socket socket = null;
            BufferedReader reader = null;
            PrintWriter writer = null;
            
            try {
                // 等待统一开始信号
                startLatch.await();
                
                long connectStartTime = System.currentTimeMillis();
                
                // 建立连接
                socket = new Socket(SERVER_HOST, SERVER_PORT);
                reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                writer = new PrintWriter(socket.getOutputStream(), true);
                
                long connectEndTime = System.currentTimeMillis();
                result.connectionTime = connectEndTime - connectStartTime;
                
                // 读取欢迎消息
                reader.readLine(); // 欢迎消息1
                reader.readLine(); // 欢迎消息2
                
                successfulConnections.incrementAndGet();
                
                // 发送测试消息
                for (int i = 0; i < messageCount; i++) {
                    long messageStartTime = System.currentTimeMillis();
                    
                    String message = "客户端-" + clientId + "-消息-" + (i + 1);
                    writer.println(message);
                    
                    // 等待服务器回复
                    String response = reader.readLine();
                    
                    long messageEndTime = System.currentTimeMillis();
                    long responseTime = messageEndTime - messageStartTime;
                    
                    result.addMessageTime(responseTime);
                    totalResponseTime.addAndGet(responseTime);
                    totalMessages.incrementAndGet();
                    
                    if (response == null) {
                        result.failedMessages++;
                    } else {
                        result.successfulMessages++;
                    }
                }
                
                // 发送退出消息
                writer.println("quit");
                reader.readLine(); // 读取再见消息
                
            } catch (Exception e) {
                result.exception = e;
                failedConnections.incrementAndGet();
            } finally {
                // 清理资源
                closeResources(socket, reader, writer);
                result.endTime = System.currentTimeMillis();
            }
            
            return result;
        }
        
        private void closeResources(Socket socket, BufferedReader reader, PrintWriter writer) {
            try {
                if (reader != null) reader.close();
                if (writer != null) writer.close();
                if (socket != null && !socket.isClosed()) socket.close();
            } catch (IOException e) {
                // 忽略关闭时的异常
            }
        }
    }
    
    /**
     * 测试结果类
     */
    private static class TestResult {
        final int clientId;
        long connectionTime;
        int successfulMessages = 0;
        int failedMessages = 0;
        long totalMessageTime = 0;
        long minMessageTime = Long.MAX_VALUE;
        long maxMessageTime = 0;
        Exception exception;
        long startTime;
        long endTime;
        
        public TestResult(int clientId) {
            this.clientId = clientId;
            this.startTime = System.currentTimeMillis();
        }
        
        public void addMessageTime(long time) {
            totalMessageTime += time;
            minMessageTime = Math.min(minMessageTime, time);
            maxMessageTime = Math.max(maxMessageTime, time);
        }
        
        public double getAverageMessageTime() {
            return successfulMessages > 0 ? (double) totalMessageTime / successfulMessages : 0;
        }
    }
    
    /**
     * 运行性能测试
     */
    public static void runPerformanceTest(int clientCount, int messagePerClient) {
        System.out.println("=== BIO性能测试开始 ===");
        System.out.println("客户端数量: " + clientCount);
        System.out.println("每客户端消息数: " + messagePerClient);
        System.out.println("总消息数: " + (clientCount * messagePerClient));
        System.out.println();
        
        // 检查服务器是否运行
        if (!isServerRunning()) {
            System.err.println("错误: 服务器未运行，请先启动BIO服务器");
            return;
        }
        
        ExecutorService executor = Executors.newFixedThreadPool(clientCount);
        CountDownLatch startLatch = new CountDownLatch(1);
        
        // 重置统计数据
        successfulConnections.set(0);
        failedConnections.set(0);
        totalResponseTime.set(0);
        totalMessages.set(0);
        
        long testStartTime = System.currentTimeMillis();
        
        // 提交所有测试任务
        CompletionService<TestResult> completionService = new ExecutorCompletionService<>(executor);
        for (int i = 1; i <= clientCount; i++) {
            completionService.submit(new PerformanceTestClient(i, messagePerClient, startLatch));
        }
        
        // 统一开始测试
        startLatch.countDown();
        System.out.println("性能测试开始执行...");
        
        // 收集测试结果
        int completedClients = 0;
        long totalConnectionTime = 0;
        
        try {
            for (int i = 0; i < clientCount; i++) {
                Future<TestResult> future = completionService.take();
                TestResult result = future.get();
                
                completedClients++;
                totalConnectionTime += result.connectionTime;
                
                if (result.exception != null) {
                    System.err.println("客户端-" + result.clientId + " 发生异常: " + result.exception.getMessage());
                }
                
                // 显示进度
                if (completedClients % Math.max(1, clientCount / 10) == 0) {
                    System.out.println("已完成: " + completedClients + "/" + clientCount + " 客户端");
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            System.err.println("收集测试结果时发生错误: " + e.getMessage());
        }
        
        long testEndTime = System.currentTimeMillis();
        
        // 输出测试结果
        printTestResults(testStartTime, testEndTime, clientCount, messagePerClient, 
                        totalConnectionTime, completedClients);
        
        executor.shutdown();
    }
    
    /**
     * 打印测试结果
     */
    private static void printTestResults(long startTime, long endTime, int clientCount, 
                                       int messagePerClient, long totalConnectionTime, 
                                       int completedClients) {
        long totalTestTime = endTime - startTime;
        double avgConnectionTime = completedClients > 0 ? (double) totalConnectionTime / completedClients : 0;
        double avgResponseTime = totalMessages.get() > 0 ? (double) totalResponseTime.get() / totalMessages.get() : 0;
        
        System.out.println("\n=== BIO性能测试结果 ===");
        System.out.println("总测试时间: " + totalTestTime + " ms");
        System.out.println("成功连接数: " + successfulConnections.get() + "/" + clientCount);
        System.out.println("失败连接数: " + failedConnections.get());
        System.out.println("平均连接时间: " + String.format("%.2f", avgConnectionTime) + " ms");
        System.out.println("总消息数: " + totalMessages.get());
        System.out.println("平均响应时间: " + String.format("%.2f", avgResponseTime) + " ms");
        
        if (totalTestTime > 0) {
            double throughput = (double) totalMessages.get() * 1000 / totalTestTime;
            System.out.println("消息吞吐量: " + String.format("%.2f", throughput) + " 消息/秒");
        }
        
        // 计算并发性能指标
        if (successfulConnections.get() > 0) {
            double concurrencyLevel = (double) successfulConnections.get() * messagePerClient * 1000 / totalTestTime;
            System.out.println("并发处理能力: " + String.format("%.2f", concurrencyLevel) + " 请求/秒");
        }
        
        System.out.println("\n=== 性能分析 ===");
        if (failedConnections.get() > 0) {
            System.out.println("⚠️  有连接失败，可能是服务器负载过高或线程池已满");
        }
        if (avgResponseTime > 1000) {
            System.out.println("⚠️  平均响应时间较长，BIO模型在高并发下性能有限");
        }
        if (successfulConnections.get() == clientCount) {
            System.out.println("✅ 所有连接都成功建立，服务器运行稳定");
        }
    }
    
    /**
     * 检查服务器是否运行
     */
    private static boolean isServerRunning() {
        try (Socket testSocket = new Socket(SERVER_HOST, SERVER_PORT)) {
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        // 可以通过命令行参数指定测试参数
        int clientCount = args.length > 0 ? Integer.parseInt(args[0]) : 100;
        int messagePerClient = args.length > 1 ? Integer.parseInt(args[1]) : 5;
        
        runPerformanceTest(clientCount, messagePerClient);
    }
}
